/* Basic Styles */
body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    background-color: #f4f4f4;
    color: #333;
    padding: 20px;
}

.container {
    max-width: 1100px;
    margin: auto;
    overflow: hidden;
    padding: 0 2rem;
}

/* Form Styles */
.form-container {
    max-width: 500px;
    margin: 2rem auto;
    padding: 2rem;
    background: #fff;
    border: 1px solid #ddd;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
}

.form-group input[type='text'],
.form-group input[type='email'],
.form-group input[type='password'] {
    width: 100%;
    padding: 0.7rem;
    font-size: 1rem;
    border: 1px solid #ccc;
}

.btn {
    display: inline-block;
    background: #333;
    color: #fff;
    padding: 0.4rem 1.3rem;
    font-size: 1rem;
    border: none;
    cursor: pointer;
    margin-right: 0.5rem;
    outline: none;
    transition: opacity 0.2s ease-in;
}

.btn-primary {
    background: #007bff;
}

.btn-block {
    display: block;
    width: 100%;
    padding: 0.8rem;
}

.text-primary {
    color: #007bff;
}

p {
    margin-top: 1rem;
}

/* Utilities */
.grid-2 {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 2rem;
}

.card {
    background: #fff;
    padding: 1rem;
    border: #ccc 1px dotted;
    margin: 0.7rem 0;
}

.bg-light {
    background: #f4f4f4;
    border: #ccc 1px solid;
}

.btn-dark {
    background: #333;
    color: #fff;
}

.btn-danger {
    background: #dc3545;
    color: #fff;
}

.text-left {
    text-align: left;
}
