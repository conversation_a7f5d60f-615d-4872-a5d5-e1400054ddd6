# OA系统数据字典

## 📋 概述

本文档定义了OA系统中所有数据实体、字段规范、枚举值和约束条件，为系统集成和数据迁移提供标准参考。

## 🏗️ 数据库表结构

### 1. 用户相关表

#### users - 用户表
| 字段名 | 类型 | 长度 | 必填 | 默认值 | 说明 | 索引 |
|--------|------|------|------|--------|------|------|
| id | ObjectId | - | ✓ | auto | 用户唯一标识 | PRIMARY |
| username | String | 50 | ✓ | - | 用户名 | UNIQUE |
| email | String | 100 | ✓ | - | 邮箱地址 | UNIQUE |
| password_hash | String | 255 | ✓ | - | 密码哈希值 | - |
| name | String | 100 | ✓ | - | 真实姓名 | - |
| avatar | String | 255 | ✗ | null | 头像URL | - |
| phone | String | 20 | ✗ | null | 手机号码 | - |
| status | Enum | - | ✓ | active | 用户状态 | INDEX |
| external_id | String | 100 | ✗ | null | 外部系统用户ID | INDEX |
| department_id | ObjectId | - | ✗ | null | 部门ID | INDEX |
| roles | Array | - | ✓ | ['employee'] | 用户角色 | - |
| permissions | Array | - | ✓ | [] | 用户权限 | - |
| last_login_at | Date | - | ✗ | null | 最后登录时间 | - |
| created_at | Date | - | ✓ | now | 创建时间 | INDEX |
| updated_at | Date | - | ✓ | now | 更新时间 | - |

**枚举值定义**:
```typescript
enum UserStatus {
  ACTIVE = 'active',        // 激活
  INACTIVE = 'inactive',    // 未激活
  SUSPENDED = 'suspended',  // 暂停
  DELETED = 'deleted'       // 已删除
}

enum UserRole {
  ADMIN = 'admin',          // 管理员
  MANAGER = 'manager',      // 经理
  EMPLOYEE = 'employee',    // 员工
  GUEST = 'guest'           // 访客
}
```

#### departments - 部门表
| 字段名 | 类型 | 长度 | 必填 | 默认值 | 说明 | 索引 |
|--------|------|------|------|--------|------|------|
| id | ObjectId | - | ✓ | auto | 部门唯一标识 | PRIMARY |
| name | String | 100 | ✓ | - | 部门名称 | INDEX |
| description | String | 500 | ✗ | null | 部门描述 | - |
| parent_id | ObjectId | - | ✗ | null | 父部门ID | INDEX |
| manager_id | ObjectId | - | ✗ | null | 部门经理ID | INDEX |
| external_id | String | 100 | ✗ | null | 外部系统部门ID | INDEX |
| sort_order | Number | - | ✓ | 0 | 排序顺序 | - |
| is_active | Boolean | - | ✓ | true | 是否激活 | INDEX |
| created_at | Date | - | ✓ | now | 创建时间 | - |
| updated_at | Date | - | ✓ | now | 更新时间 | - |

### 2. 项目管理表

#### projects - 项目表
| 字段名 | 类型 | 长度 | 必填 | 默认值 | 说明 | 索引 |
|--------|------|------|------|--------|------|------|
| id | ObjectId | - | ✓ | auto | 项目唯一标识 | PRIMARY |
| name | String | 200 | ✓ | - | 项目名称 | INDEX |
| description | Text | - | ✗ | null | 项目描述 | - |
| status | Enum | - | ✓ | planning | 项目状态 | INDEX |
| priority | Enum | - | ✓ | medium | 优先级 | INDEX |
| progress | Number | - | ✓ | 0 | 进度百分比(0-100) | - |
| start_date | Date | - | ✗ | null | 开始日期 | INDEX |
| end_date | Date | - | ✗ | null | 结束日期 | INDEX |
| actual_end_date | Date | - | ✗ | null | 实际结束日期 | - |
| owner_id | ObjectId | - | ✓ | - | 项目负责人ID | INDEX |
| created_by | ObjectId | - | ✓ | - | 创建者ID | INDEX |
| budget | Number | - | ✗ | null | 项目预算 | - |
| tags | Array | - | ✓ | [] | 项目标签 | INDEX |
| is_archived | Boolean | - | ✓ | false | 是否归档 | INDEX |
| archived_at | Date | - | ✗ | null | 归档时间 | - |
| created_at | Date | - | ✓ | now | 创建时间 | INDEX |
| updated_at | Date | - | ✓ | now | 更新时间 | - |

**枚举值定义**:
```typescript
enum ProjectStatus {
  PLANNING = 'planning',    // 规划中
  ACTIVE = 'active',        // 进行中
  ON_HOLD = 'on_hold',      // 暂停
  COMPLETED = 'completed',  // 已完成
  CANCELLED = 'cancelled'   // 已取消
}

enum Priority {
  LOW = 'low',              // 低
  MEDIUM = 'medium',        // 中
  HIGH = 'high',            // 高
  URGENT = 'urgent'         // 紧急
}
```

#### project_members - 项目成员表
| 字段名 | 类型 | 长度 | 必填 | 默认值 | 说明 | 索引 |
|--------|------|------|------|--------|------|------|
| id | ObjectId | - | ✓ | auto | 记录唯一标识 | PRIMARY |
| project_id | ObjectId | - | ✓ | - | 项目ID | INDEX |
| user_id | ObjectId | - | ✓ | - | 用户ID | INDEX |
| role | Enum | - | ✓ | member | 成员角色 | - |
| joined_at | Date | - | ✓ | now | 加入时间 | - |
| left_at | Date | - | ✗ | null | 离开时间 | - |
| is_active | Boolean | - | ✓ | true | 是否激活 | INDEX |

**复合索引**: (project_id, user_id) UNIQUE

```typescript
enum ProjectMemberRole {
  OWNER = 'owner',          // 负责人
  ADMIN = 'admin',          // 管理员
  MEMBER = 'member',        // 成员
  VIEWER = 'viewer'         // 查看者
}
```

#### tasks - 任务表
| 字段名 | 类型 | 长度 | 必填 | 默认值 | 说明 | 索引 |
|--------|------|------|------|--------|------|------|
| id | ObjectId | - | ✓ | auto | 任务唯一标识 | PRIMARY |
| title | String | 200 | ✓ | - | 任务标题 | INDEX |
| description | Text | - | ✗ | null | 任务描述 | - |
| project_id | ObjectId | - | ✓ | - | 所属项目ID | INDEX |
| parent_task_id | ObjectId | - | ✗ | null | 父任务ID | INDEX |
| assigned_to | ObjectId | - | ✗ | null | 分配给用户ID | INDEX |
| status | Enum | - | ✓ | todo | 任务状态 | INDEX |
| priority | Enum | - | ✓ | medium | 优先级 | INDEX |
| progress | Number | - | ✓ | 0 | 进度百分比(0-100) | - |
| estimated_hours | Number | - | ✗ | null | 预估工时 | - |
| actual_hours | Number | - | ✗ | null | 实际工时 | - |
| start_date | Date | - | ✗ | null | 开始日期 | INDEX |
| due_date | Date | - | ✗ | null | 截止日期 | INDEX |
| completed_at | Date | - | ✗ | null | 完成时间 | - |
| created_by | ObjectId | - | ✓ | - | 创建者ID | INDEX |
| tags | Array | - | ✓ | [] | 任务标签 | INDEX |
| created_at | Date | - | ✓ | now | 创建时间 | INDEX |
| updated_at | Date | - | ✓ | now | 更新时间 | - |

```typescript
enum TaskStatus {
  TODO = 'todo',            // 待办
  IN_PROGRESS = 'in_progress', // 进行中
  REVIEW = 'review',        // 待审核
  COMPLETED = 'completed',  // 已完成
  CANCELLED = 'cancelled'   // 已取消
}
```

### 3. 文件管理表

#### files - 文件表
| 字段名 | 类型 | 长度 | 必填 | 默认值 | 说明 | 索引 |
|--------|------|------|------|--------|------|------|
| id | ObjectId | - | ✓ | auto | 文件唯一标识 | PRIMARY |
| filename | String | 255 | ✓ | - | 存储文件名 | - |
| original_name | String | 255 | ✓ | - | 原始文件名 | INDEX |
| mimetype | String | 100 | ✓ | - | MIME类型 | INDEX |
| size | Number | - | ✓ | - | 文件大小(字节) | - |
| category | Enum | - | ✓ | other | 文件分类 | INDEX |
| path | String | 500 | ✓ | - | 存储路径 | - |
| hash | String | 64 | ✗ | null | 文件哈希值 | INDEX |
| uploaded_by | ObjectId | - | ✓ | - | 上传者ID | INDEX |
| project_id | ObjectId | - | ✗ | null | 关联项目ID | INDEX |
| task_id | ObjectId | - | ✗ | null | 关联任务ID | INDEX |
| description | String | 500 | ✗ | null | 文件描述 | - |
| tags | Array | - | ✓ | [] | 文件标签 | INDEX |
| is_public | Boolean | - | ✓ | false | 是否公开 | INDEX |
| download_count | Number | - | ✓ | 0 | 下载次数 | - |
| version | Number | - | ✓ | 1 | 版本号 | - |
| parent_file_id | ObjectId | - | ✗ | null | 父文件ID(版本控制) | INDEX |
| is_deleted | Boolean | - | ✓ | false | 是否删除 | INDEX |
| deleted_at | Date | - | ✗ | null | 删除时间 | - |
| created_at | Date | - | ✓ | now | 创建时间 | INDEX |
| updated_at | Date | - | ✓ | now | 更新时间 | - |

```typescript
enum FileCategory {
  DOCUMENT = 'document',    // 文档
  IMAGE = 'image',          // 图片
  VIDEO = 'video',          // 视频
  AUDIO = 'audio',          // 音频
  ARCHIVE = 'archive',      // 压缩包
  OTHER = 'other'           // 其他
}
```

#### file_permissions - 文件权限表
| 字段名 | 类型 | 长度 | 必填 | 默认值 | 说明 | 索引 |
|--------|------|------|------|--------|------|------|
| id | ObjectId | - | ✓ | auto | 权限记录ID | PRIMARY |
| file_id | ObjectId | - | ✓ | - | 文件ID | INDEX |
| user_id | ObjectId | - | ✓ | - | 用户ID | INDEX |
| permission | Enum | - | ✓ | read | 权限类型 | - |
| granted_by | ObjectId | - | ✓ | - | 授权者ID | INDEX |
| granted_at | Date | - | ✓ | now | 授权时间 | - |

**复合索引**: (file_id, user_id) UNIQUE

```typescript
enum FilePermission {
  READ = 'read',            // 读取
  WRITE = 'write',          // 写入
  ADMIN = 'admin'           // 管理
}
```

### 4. 消息通讯表

#### chat_rooms - 聊天室表
| 字段名 | 类型 | 长度 | 必填 | 默认值 | 说明 | 索引 |
|--------|------|------|------|--------|------|------|
| id | ObjectId | - | ✓ | auto | 聊天室唯一标识 | PRIMARY |
| name | String | 100 | ✓ | - | 聊天室名称 | INDEX |
| description | String | 500 | ✗ | null | 聊天室描述 | - |
| type | Enum | - | ✓ | group | 聊天室类型 | INDEX |
| avatar | String | 255 | ✗ | null | 聊天室头像 | - |
| project_id | ObjectId | - | ✗ | null | 关联项目ID | INDEX |
| created_by | ObjectId | - | ✓ | - | 创建者ID | INDEX |
| max_members | Number | - | ✓ | 100 | 最大成员数 | - |
| is_public | Boolean | - | ✓ | false | 是否公开 | INDEX |
| allow_file_sharing | Boolean | - | ✓ | true | 允许文件分享 | - |
| allow_member_invite | Boolean | - | ✓ | true | 允许成员邀请 | - |
| message_retention_days | Number | - | ✓ | 365 | 消息保留天数 | - |
| last_message_id | ObjectId | - | ✗ | null | 最后消息ID | - |
| last_activity | Date | - | ✓ | now | 最后活动时间 | INDEX |
| is_archived | Boolean | - | ✓ | false | 是否归档 | INDEX |
| archived_at | Date | - | ✗ | null | 归档时间 | - |
| created_at | Date | - | ✓ | now | 创建时间 | INDEX |
| updated_at | Date | - | ✓ | now | 更新时间 | - |

```typescript
enum ChatRoomType {
  PRIVATE = 'private',      // 私聊
  GROUP = 'group',          // 群聊
  PROJECT = 'project',      // 项目群
  PUBLIC = 'public'         // 公开群
}
```

#### chat_room_members - 聊天室成员表
| 字段名 | 类型 | 长度 | 必填 | 默认值 | 说明 | 索引 |
|--------|------|------|------|--------|------|------|
| id | ObjectId | - | ✓ | auto | 成员记录ID | PRIMARY |
| room_id | ObjectId | - | ✓ | - | 聊天室ID | INDEX |
| user_id | ObjectId | - | ✓ | - | 用户ID | INDEX |
| role | Enum | - | ✓ | member | 成员角色 | - |
| joined_at | Date | - | ✓ | now | 加入时间 | - |
| last_seen | Date | - | ✓ | now | 最后在线时间 | - |
| is_active | Boolean | - | ✓ | true | 是否激活 | INDEX |
| is_muted | Boolean | - | ✓ | false | 是否静音 | - |

**复合索引**: (room_id, user_id) UNIQUE

```typescript
enum ChatRoomMemberRole {
  OWNER = 'owner',          // 群主
  ADMIN = 'admin',          // 管理员
  MEMBER = 'member'         // 成员
}
```

#### messages - 消息表
| 字段名 | 类型 | 长度 | 必填 | 默认值 | 说明 | 索引 |
|--------|------|------|------|--------|------|------|
| id | ObjectId | - | ✓ | auto | 消息唯一标识 | PRIMARY |
| content | Text | - | ✗ | null | 消息内容 | - |
| message_type | Enum | - | ✓ | text | 消息类型 | INDEX |
| sender_id | ObjectId | - | ✓ | - | 发送者ID | INDEX |
| room_id | ObjectId | - | ✓ | - | 聊天室ID | INDEX |
| reply_to_id | ObjectId | - | ✗ | null | 回复消息ID | INDEX |
| file_id | ObjectId | - | ✗ | null | 文件ID | INDEX |
| file_url | String | 500 | ✗ | null | 文件URL | - |
| file_name | String | 255 | ✗ | null | 文件名 | - |
| file_size | Number | - | ✗ | null | 文件大小 | - |
| is_edited | Boolean | - | ✓ | false | 是否编辑 | - |
| edited_at | Date | - | ✗ | null | 编辑时间 | - |
| is_deleted | Boolean | - | ✓ | false | 是否删除 | INDEX |
| deleted_at | Date | - | ✗ | null | 删除时间 | - |
| created_at | Date | - | ✓ | now | 创建时间 | INDEX |
| updated_at | Date | - | ✓ | now | 更新时间 | - |

**复合索引**: (room_id, created_at) DESC

```typescript
enum MessageType {
  TEXT = 'text',            // 文本消息
  FILE = 'file',            // 文件消息
  IMAGE = 'image',          // 图片消息
  SYSTEM = 'system'         // 系统消息
}
```

#### message_read_status - 消息已读状态表
| 字段名 | 类型 | 长度 | 必填 | 默认值 | 说明 | 索引 |
|--------|------|------|------|--------|------|------|
| id | ObjectId | - | ✓ | auto | 记录ID | PRIMARY |
| message_id | ObjectId | - | ✓ | - | 消息ID | INDEX |
| user_id | ObjectId | - | ✓ | - | 用户ID | INDEX |
| read_at | Date | - | ✓ | now | 已读时间 | - |

**复合索引**: (message_id, user_id) UNIQUE

#### message_reactions - 消息反应表
| 字段名 | 类型 | 长度 | 必填 | 默认值 | 说明 | 索引 |
|--------|------|------|------|--------|------|------|
| id | ObjectId | - | ✓ | auto | 反应记录ID | PRIMARY |
| message_id | ObjectId | - | ✓ | - | 消息ID | INDEX |
| user_id | ObjectId | - | ✓ | - | 用户ID | INDEX |
| emoji | String | 10 | ✓ | - | 表情符号 | - |
| created_at | Date | - | ✓ | now | 创建时间 | - |

**复合索引**: (message_id, user_id, emoji) UNIQUE

## 🔗 关系约束

### 外键关系
```sql
-- 用户部门关系
users.department_id -> departments.id

-- 项目关系
projects.owner_id -> users.id
projects.created_by -> users.id
project_members.project_id -> projects.id
project_members.user_id -> users.id

-- 任务关系
tasks.project_id -> projects.id
tasks.parent_task_id -> tasks.id
tasks.assigned_to -> users.id
tasks.created_by -> users.id

-- 文件关系
files.uploaded_by -> users.id
files.project_id -> projects.id
files.task_id -> tasks.id
files.parent_file_id -> files.id
file_permissions.file_id -> files.id
file_permissions.user_id -> users.id
file_permissions.granted_by -> users.id

-- 消息关系
chat_rooms.project_id -> projects.id
chat_rooms.created_by -> users.id
chat_room_members.room_id -> chat_rooms.id
chat_room_members.user_id -> users.id
messages.sender_id -> users.id
messages.room_id -> chat_rooms.id
messages.reply_to_id -> messages.id
messages.file_id -> files.id
message_read_status.message_id -> messages.id
message_read_status.user_id -> users.id
message_reactions.message_id -> messages.id
message_reactions.user_id -> users.id
```

## 📊 数据验证规则

### 字段验证
```typescript
// 用户名验证
username: /^[a-zA-Z0-9_]{3,50}$/

// 邮箱验证
email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/

// 手机号验证 (中国)
phone: /^1[3-9]\d{9}$/

// 密码强度 (至少8位，包含字母和数字)
password: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/

// 文件名验证 (不包含特殊字符)
filename: /^[^<>:"/\\|?*\x00-\x1f]+$/

// 项目名称验证
project_name: /^.{1,200}$/

// 标签验证 (字母数字中文下划线)
tag: /^[\u4e00-\u9fa5a-zA-Z0-9_]{1,20}$/
```

### 业务规则
```typescript
// 进度百分比
progress: 0 <= value <= 100

// 文件大小限制
file_size: value <= 10 * 1024 * 1024 // 10MB

// 项目日期
start_date <= end_date

// 任务日期
start_date <= due_date

// 聊天室成员数量
room_members.length <= max_members

// 消息长度限制
message_content.length <= 5000

// 用户角色层级
admin > manager > employee > guest
```

这份数据字典提供了完整的数据结构定义，包括字段规范、约束条件、枚举值和验证规则，为系统集成和数据迁移提供了标准参考。
