# 小型OA系统技术架构设计文档

## 1. 技术栈选型

### 1.1 后端技术栈
- **编程语言**: Python 3.9+
- **Web框架**: Django 4.2 LTS
- **API框架**: Django REST Framework
- **实时通讯**: Django Channels + WebSocket
- **任务队列**: Celery + Redis
- **数据库**: PostgreSQL 14+ (主库) + Redis (缓存)
- **文件存储**: 本地存储 + 可选云存储(阿里云OSS)

### 1.2 前端技术栈
- **框架**: Vue.js 3.x + TypeScript
- **UI组件库**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **HTTP客户端**: Axios
- **实时通讯**: Socket.io-client
- **构建工具**: Vite

### 1.3 开发工具
- **代码编辑器**: VS Code / PyCharm
- **版本控制**: Git + GitHub/GitLab
- **API文档**: Swagger/OpenAPI
- **容器化**: Docker + Docker Compose
- **CI/CD**: GitHub Actions

## 2. 系统架构设计

### 2.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │    │   移动端应用    │    │   第三方集成    │
│   (Vue.js)      │    │   (可选)        │    │   (API)         │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   API网关       │
                    │   (Nginx)       │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   应用服务层    │
                    │   (Django)      │
                    └─────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   数据库        │    │   缓存层        │    │   文件存储      │
│   (PostgreSQL)  │    │   (Redis)       │    │   (Local/OSS)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2.2 分层架构
- **表现层**: Vue.js前端应用
- **API层**: Django REST Framework
- **业务逻辑层**: Django Models + Services
- **数据访问层**: Django ORM
- **数据存储层**: PostgreSQL + Redis

### 2.3 微服务划分(可选)
- **用户服务**: 用户管理、认证授权
- **消息服务**: 即时通讯、通知推送
- **项目服务**: 项目管理、任务管理
- **文件服务**: 文件上传、存储、管理
- **基础服务**: 公告、考勤、通讯录

## 3. 数据库设计

### 3.1 核心数据表

#### 用户相关表
```sql
-- 用户表
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(50),
    last_name VARCHAR(50),
    avatar VARCHAR(255),
    phone VARCHAR(20),
    department_id INTEGER,
    role VARCHAR(20) DEFAULT 'employee',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 部门表
CREATE TABLE departments (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    parent_id INTEGER,
    manager_id INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 项目管理表
```sql
-- 项目表
CREATE TABLE projects (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    status VARCHAR(20) DEFAULT 'active',
    priority VARCHAR(10) DEFAULT 'medium',
    start_date DATE,
    end_date DATE,
    created_by INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 任务表
CREATE TABLE tasks (
    id SERIAL PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    project_id INTEGER NOT NULL,
    assigned_to INTEGER,
    status VARCHAR(20) DEFAULT 'todo',
    priority VARCHAR(10) DEFAULT 'medium',
    start_date DATE,
    due_date DATE,
    completed_at TIMESTAMP,
    created_by INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 消息通讯表
```sql
-- 聊天室表
CREATE TABLE chat_rooms (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100),
    type VARCHAR(20) NOT NULL, -- 'private', 'group'
    created_by INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 消息表
CREATE TABLE messages (
    id SERIAL PRIMARY KEY,
    room_id INTEGER NOT NULL,
    sender_id INTEGER NOT NULL,
    content TEXT NOT NULL,
    message_type VARCHAR(20) DEFAULT 'text', -- 'text', 'file', 'image'
    file_url VARCHAR(255),
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 3.2 索引策略
```sql
-- 用户表索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_department ON users(department_id);

-- 项目表索引
CREATE INDEX idx_projects_status ON projects(status);
CREATE INDEX idx_projects_created_by ON projects(created_by);

-- 任务表索引
CREATE INDEX idx_tasks_project ON tasks(project_id);
CREATE INDEX idx_tasks_assigned ON tasks(assigned_to);
CREATE INDEX idx_tasks_status ON tasks(status);

-- 消息表索引
CREATE INDEX idx_messages_room ON messages(room_id);
CREATE INDEX idx_messages_sender ON messages(sender_id);
CREATE INDEX idx_messages_created ON messages(created_at);
```

## 4. API设计规范

### 4.1 RESTful API设计
```
# 用户管理
GET    /api/v1/users/              # 获取用户列表
POST   /api/v1/users/              # 创建用户
GET    /api/v1/users/{id}/         # 获取用户详情
PUT    /api/v1/users/{id}/         # 更新用户信息
DELETE /api/v1/users/{id}/         # 删除用户

# 项目管理
GET    /api/v1/projects/           # 获取项目列表
POST   /api/v1/projects/           # 创建项目
GET    /api/v1/projects/{id}/      # 获取项目详情
PUT    /api/v1/projects/{id}/      # 更新项目
DELETE /api/v1/projects/{id}/      # 删除项目

# 任务管理
GET    /api/v1/projects/{id}/tasks/     # 获取项目任务
POST   /api/v1/projects/{id}/tasks/     # 创建任务
PUT    /api/v1/tasks/{id}/              # 更新任务
DELETE /api/v1/tasks/{id}/              # 删除任务

# 文件管理
POST   /api/v1/files/upload/       # 文件上传
GET    /api/v1/files/{id}/         # 文件下载
DELETE /api/v1/files/{id}/         # 删除文件
```

### 4.2 WebSocket API设计
```javascript
// 连接认证
{
  "type": "auth",
  "token": "jwt_token_here"
}

// 发送消息
{
  "type": "message",
  "room_id": 123,
  "content": "Hello World",
  "message_type": "text"
}

// 接收消息
{
  "type": "message",
  "id": 456,
  "room_id": 123,
  "sender": {
    "id": 1,
    "username": "john",
    "avatar": "avatar_url"
  },
  "content": "Hello World",
  "created_at": "2024-01-01T10:00:00Z"
}
```

## 5. 安全设计

### 5.1 认证授权
- **JWT Token**: 用户认证
- **RBAC**: 基于角色的访问控制
- **API限流**: 防止恶意请求
- **CORS配置**: 跨域请求控制

### 5.2 数据安全
- **密码加密**: bcrypt哈希
- **数据传输**: HTTPS加密
- **SQL注入防护**: ORM参数化查询
- **XSS防护**: 输入输出过滤

### 5.3 文件安全
- **文件类型检查**: 白名单机制
- **文件大小限制**: 防止资源耗尽
- **病毒扫描**: 可选集成
- **访问权限**: 基于用户权限

## 6. 性能优化

### 6.1 数据库优化
- **连接池**: 数据库连接复用
- **查询优化**: 索引优化、查询缓存
- **读写分离**: 主从数据库(可选)
- **分页查询**: 大数据量分页处理

### 6.2 缓存策略
- **Redis缓存**: 热点数据缓存
- **浏览器缓存**: 静态资源缓存
- **CDN加速**: 文件分发网络
- **API缓存**: 接口响应缓存

### 6.3 前端优化
- **代码分割**: 按需加载
- **资源压缩**: Gzip压缩
- **图片优化**: WebP格式、懒加载
- **PWA**: 渐进式Web应用

## 7. 部署架构

### 7.1 单机部署
```yaml
# docker-compose.yml
version: '3.8'
services:
  web:
    build: .
    ports:
      - "8000:8000"
    depends_on:
      - db
      - redis
    environment:
      - DATABASE_URL=******************************/oa_db
      - REDIS_URL=redis://redis:6379/0

  db:
    image: postgres:14
    environment:
      - POSTGRES_DB=oa_db
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=pass
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
```

### 7.2 监控告警
- **应用监控**: Sentry错误监控
- **性能监控**: APM工具
- **日志管理**: ELK Stack
- **健康检查**: 服务状态监控
