/* 聊天容器 */
.chat-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

/* 移动端头部 */
.mobile-chat-header {
  display: none;
  background: #fff;
  border-bottom: 1px solid #e0e0e0;
  padding: 12px 16px;
  position: sticky;
  top: 0;
  z-index: 100;
}

.chat-header-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.back-btn {
  background: none;
  border: none;
  font-size: 16px;
  color: #007bff;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
}

.back-btn:hover {
  background-color: #f0f8ff;
}

.room-title {
  font-weight: 600;
  font-size: 16px;
  color: #333;
}

/* 聊天内容区域 */
.chat-content {
  flex: 1;
  display: flex;
  min-height: 0;
}

/* 聊天侧边栏 */
.chat-sidebar {
  width: 320px;
  background: #fff;
  border-right: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  transition: transform 0.3s ease;
}

.chat-sidebar.hidden {
  transform: translateX(-100%);
  position: absolute;
  z-index: -1;
}

/* 聊天主区域 */
.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  transition: transform 0.3s ease;
}

.chat-main.hidden {
  transform: translateX(100%);
  position: absolute;
  z-index: -1;
}

/* 欢迎页面 */
.chat-welcome {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  padding: 40px;
}

.welcome-content {
  text-align: center;
  max-width: 400px;
}

.welcome-icon {
  font-size: 64px;
  margin-bottom: 24px;
}

.welcome-content h3 {
  font-size: 24px;
  color: #333;
  margin-bottom: 12px;
  font-weight: 600;
}

.welcome-content p {
  color: #666;
  font-size: 16px;
  line-height: 1.5;
  margin-bottom: 32px;
}

.welcome-features {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-top: 32px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  font-size: 14px;
  color: #555;
}

.feature-icon {
  font-size: 18px;
}

/* 移动端快捷操作 */
.mobile-chat-actions {
  display: none;
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
}

.action-btn {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: #007bff;
  color: white;
  border: none;
  font-size: 24px;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: #0056b3;
  transform: scale(1.1);
}

/* 登录提示 */
.chat-login-prompt {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fff;
  text-align: center;
  padding: 40px;
}

.chat-login-prompt h3 {
  font-size: 24px;
  color: #333;
  margin-bottom: 12px;
}

.chat-login-prompt p {
  color: #666;
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mobile-chat-header {
    display: block;
  }

  .chat-sidebar {
    width: 100%;
    position: absolute;
    height: calc(100vh - 60px);
    z-index: 10;
  }

  .chat-main {
    width: 100%;
    position: absolute;
    height: calc(100vh - 60px);
    z-index: 10;
  }

  .chat-sidebar.hidden,
  .chat-main.hidden {
    position: absolute;
    z-index: -1;
  }

  .mobile-chat-actions {
    display: block;
  }

  .welcome-features {
    grid-template-columns: 1fr;
  }

  .welcome-content {
    padding: 20px;
  }

  .welcome-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }

  .welcome-content h3 {
    font-size: 20px;
    margin-bottom: 8px;
  }

  .welcome-content p {
    font-size: 14px;
    margin-bottom: 24px;
  }
}

@media (max-width: 480px) {
  .chat-container {
    height: 100vh;
  }

  .mobile-chat-header {
    padding: 8px 12px;
  }

  .chat-header-content h3 {
    font-size: 18px;
  }

  .room-title {
    font-size: 14px;
  }

  .back-btn {
    font-size: 14px;
    padding: 2px 6px;
  }

  .action-btn {
    width: 48px;
    height: 48px;
    font-size: 20px;
  }

  .mobile-chat-actions {
    bottom: 16px;
    right: 16px;
  }
}
