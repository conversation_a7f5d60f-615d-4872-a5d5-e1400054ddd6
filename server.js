const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');

const app = express();
const server = http.createServer(app);

// Socket.io配置
const io = socketIo(server, {
    cors: {
        origin: "http://localhost:3000", // React开发服务器地址
        methods: ["GET", "POST"],
        credentials: true
    }
});

// --- Middleware ---
// Enable Cross-Origin Resource Sharing
app.use(cors({
    origin: "http://localhost:3000",
    credentials: true
}));

// Enable express to parse JSON bodies from POST/PUT requests
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 静态文件服务 - 用于文件下载
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// --- Database Connection ---
// We will connect to MongoDB. For now, I'll use a placeholder URI.
// In a real application, this should be stored in an environment variable.
const dbURI = 'mongodb://localhost:27017/simple-oa';

mongoose.connect(dbURI, { useNewUrlParser: true, useUnifiedTopology: true })
    .then(() => console.log('MongoDB connected successfully.'))
    .catch(err => console.log('MongoDB connection error:', err));

// --- Socket.io Setup ---
const { initializeSocketHandlers } = require('./socket/socketHandlers');
initializeSocketHandlers(io);

// --- Routes ---
// We will define our authentication routes in a separate file.
app.use('/api/auth', require('./routes/auth'));
app.use('/api/projects', require('./routes/projects'));
app.use('/api/tasks', require('./routes/tasks'));
app.use('/api/files', require('./routes/files'));
app.use('/api/messages', require('./routes/messages'));

// 健康检查端点
app.get('/api/health', (req, res) => {
    res.json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});

// --- Server Startup ---
const PORT = process.env.PORT || 5000;
server.listen(PORT, () => {
    console.log(`Server is running on port ${PORT}`);
    console.log(`Socket.io server is ready for connections`);
});
