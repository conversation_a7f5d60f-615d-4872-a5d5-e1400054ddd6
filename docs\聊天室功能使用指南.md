# 聊天室功能使用指南

## 📋 功能概述

OA系统的聊天室功能提供了完整的即时通讯解决方案，支持一对一私聊、群组聊天、文件分享等功能，让团队协作更加高效。

## 🚀 快速开始

### 1. 启动聊天功能

#### 前端集成
```jsx
// 在主应用中引入聊天组件
import Chat from './components/chat/Chat';

function App() {
  return (
    <div className="app">
      {/* 其他组件 */}
      <Chat />
    </div>
  );
}
```

#### 安装依赖
```bash
# 确保已安装Socket.io客户端
cd client
npm install socket.io-client
```

### 2. 启动服务

```bash
# 启动后端服务 (包含Socket.io)
npm run dev

# 启动前端服务
cd client
npm start
```

## 💬 核心功能使用

### 1. 聊天室管理

#### 创建群聊
1. 点击聊天室列表顶部的 ➕ 按钮
2. 填写聊天室信息：
   - **名称**: 聊天室名称 (必填)
   - **描述**: 聊天室描述 (可选)
3. 点击"创建"按钮

#### 加入聊天室
- 群聊创建后，创建者自动成为群主
- 群主和管理员可以邀请其他成员
- 公开群聊可以通过搜索加入

#### 聊天室类型
- **私聊**: 一对一对话，自动创建
- **群聊**: 多人群组，需要创建
- **项目群**: 与项目关联的讨论群
- **公开群**: 所有人都可以加入的群聊

### 2. 发送消息

#### 文本消息
1. 在消息输入框中输入文本
2. 按 `Enter` 发送，`Shift + Enter` 换行
3. 支持表情符号和特殊字符

#### 文件消息
1. 点击输入框左侧的 📎 按钮
2. 选择要发送的文件
3. 系统自动上传并发送

**支持的文件类型**:
- 图片: JPG, PNG, GIF, WebP
- 文档: PDF, Word, Excel, PowerPoint, TXT
- 压缩包: ZIP, RAR
- 音视频: MP4, AVI, MP3, WAV

#### 表情反应
1. 点击消息可以查看反应选项
2. 选择表情符号进行反应
3. 支持的表情: 👍 ❤️ 😂 😮 😢 😡

#### 回复消息
1. 长按或右键点击要回复的消息
2. 选择"回复"选项
3. 输入回复内容并发送

### 3. 消息管理

#### 消息状态
- **发送中**: 消息正在发送
- **已发送**: 消息发送成功
- **已读**: 消息被对方阅读
- **失败**: 消息发送失败

#### 消息操作
- **编辑**: 只能编辑自己的文本消息
- **删除**: 只能删除自己的消息
- **复制**: 复制消息内容
- **转发**: 转发消息到其他聊天室

#### 消息搜索
1. 使用聊天室顶部的搜索框
2. 输入关键词搜索历史消息
3. 支持搜索文本内容和文件名

### 4. 在线状态

#### 状态显示
- **绿点**: 用户在线 (5分钟内活跃)
- **灰点**: 用户离线
- **数字**: 群聊在线人数

#### 正在输入
- 当用户正在输入时，其他成员会看到"正在输入..."提示
- 停止输入2秒后自动取消提示

## 🔧 高级功能

### 1. 聊天室设置

#### 群聊管理 (群主/管理员)
```javascript
// 添加成员
const addMember = async (roomId, userId) => {
  await axios.post(`/api/messages/rooms/${roomId}/members`, {
    userIds: [userId],
    role: 'member'
  });
};

// 移除成员
const removeMember = async (roomId, userId) => {
  await axios.delete(`/api/messages/rooms/${roomId}/members/${userId}`);
};

// 设置管理员
const setAdmin = async (roomId, userId) => {
  await axios.put(`/api/messages/rooms/${roomId}/members/${userId}`, {
    role: 'admin'
  });
};
```

#### 权限设置
- **群主**: 所有权限
- **管理员**: 管理成员、删除消息
- **成员**: 发送消息、查看历史

### 2. 文件管理

#### 文件权限
```javascript
// 设置文件权限
const setFilePermission = async (fileId, userId, permission) => {
  await axios.post(`/api/files/${fileId}/permissions`, {
    userId: userId,
    permission: permission // 'read', 'write', 'admin'
  });
};
```

#### 文件预览
- **图片**: 直接在聊天中显示
- **文档**: 点击预览或下载
- **其他**: 显示文件信息和下载链接

### 3. 通知管理

#### 消息通知
```javascript
// 标记消息已读
const markAsRead = (messageId) => {
  socket.emit('mark_read', {
    messageId: messageId,
    roomId: currentRoomId
  });
};

// 标记房间所有消息已读
const markRoomAsRead = (roomId) => {
  socket.emit('mark_read', {
    roomId: roomId
  });
};
```

#### 推送通知
- 浏览器通知 (需要用户授权)
- 未读消息数量显示
- 声音提醒 (可设置)

## 🎯 使用技巧

### 1. 快捷键
- `Enter`: 发送消息
- `Shift + Enter`: 换行
- `Ctrl + /`: 显示帮助
- `Esc`: 关闭模态框

### 2. 移动端优化
- 响应式设计，支持手机和平板
- 触摸手势支持
- 移动端专用导航

### 3. 性能优化
- 消息分页加载，避免一次加载过多消息
- 图片懒加载
- 虚拟滚动 (大量消息时)

## 🔍 故障排除

### 1. 连接问题

#### Socket连接失败
```javascript
// 检查连接状态
socket.on('connect_error', (error) => {
  console.error('Socket连接失败:', error);
  // 显示重连提示
});

// 手动重连
const reconnect = () => {
  socket.disconnect();
  socket.connect();
};
```

#### 认证失败
```javascript
// 检查Token是否有效
const token = localStorage.getItem('token');
if (!token) {
  // 重新登录
  window.location.href = '/login';
}
```

### 2. 消息问题

#### 消息发送失败
1. 检查网络连接
2. 确认用户权限
3. 检查消息内容是否符合规范
4. 重试发送

#### 文件上传失败
1. 检查文件大小 (限制10MB)
2. 确认文件类型是否支持
3. 检查网络连接
4. 重新选择文件

### 3. 性能问题

#### 消息加载慢
1. 检查网络速度
2. 清理浏览器缓存
3. 减少同时打开的聊天室数量

#### 内存占用高
1. 关闭不需要的聊天室
2. 刷新页面释放内存
3. 升级浏览器版本

## 📱 API接口说明

### 1. 聊天室接口
```javascript
// 获取聊天室列表
GET /api/messages/rooms

// 创建聊天室
POST /api/messages/rooms
{
  "name": "聊天室名称",
  "description": "描述",
  "type": "group"
}

// 创建私聊
POST /api/messages/rooms/private
{
  "userId": "目标用户ID"
}
```

### 2. 消息接口
```javascript
// 获取消息历史
GET /api/messages/rooms/{roomId}/messages?page=1&limit=50

// 发送消息
POST /api/messages
{
  "roomId": "聊天室ID",
  "content": "消息内容",
  "messageType": "text"
}

// 搜索消息
GET /api/messages/search?q=关键词&roomId=聊天室ID
```

### 3. Socket事件
```javascript
// 客户端发送
socket.emit('join_room', { roomId });
socket.emit('send_message', { roomId, content, messageType });
socket.emit('typing', { roomId });
socket.emit('mark_read', { messageId, roomId });

// 服务器推送
socket.on('new_message', (data) => {});
socket.on('user_typing', (data) => {});
socket.on('message_read', (data) => {});
socket.on('user_online', (data) => {});
```

## 🔒 安全注意事项

### 1. 数据安全
- 所有消息都经过加密传输
- 文件上传有类型和大小限制
- 敏感信息不要在聊天中发送

### 2. 权限控制
- 只能查看有权限的聊天室
- 文件访问需要相应权限
- 管理操作需要管理员权限

### 3. 隐私保护
- 私聊内容只有参与者可见
- 删除的消息无法恢复
- 用户可以退出不需要的群聊

## 📞 技术支持

如果在使用过程中遇到问题，可以：

1. 查看浏览器控制台错误信息
2. 检查网络连接状态
3. 确认用户权限设置
4. 联系系统管理员

通过这个指南，您应该能够熟练使用OA系统的聊天室功能，实现高效的团队沟通和协作。
