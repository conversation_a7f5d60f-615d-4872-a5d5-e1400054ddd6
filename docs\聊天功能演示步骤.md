# 聊天功能演示步骤

## 🎯 演示目标

通过实际操作演示OA系统聊天功能的完整使用流程，包括创建聊天室、发送消息、文件分享等核心功能。

## 🚀 准备工作

### 1. 启动系统

```bash
# 1. 启动后端服务
npm run dev

# 2. 启动前端服务 (新终端)
cd client
npm start

# 3. 确保MongoDB和Redis服务运行
# Windows: 
# - MongoDB: net start MongoDB
# - Redis: redis-server

# 4. 访问系统
# 前端: http://localhost:3000
# 后端: http://localhost:5000
```

### 2. 创建测试用户

```bash
# 使用MongoDB客户端或注册页面创建几个测试用户
# 用户1: <EMAIL> / password123
# 用户2: <EMAIL> / password123  
# 用户3: <EMAIL> / password123
```

## 📋 演示步骤

### 第一步：登录和导航

1. **登录系统**
   - 访问 http://localhost:3000
   - 使用测试账号登录: <EMAIL> / password123
   - 确认登录成功，看到仪表板

2. **访问聊天功能**
   - 点击顶部导航栏的"💬 聊天"按钮
   - 或直接访问 http://localhost:3000/chat
   - 确认进入聊天界面

### 第二步：创建群聊

1. **创建第一个群聊**
   - 点击聊天室列表顶部的 ➕ 按钮
   - 填写群聊信息：
     ```
     名称: 项目讨论组
     描述: 用于讨论项目相关事宜
     类型: group
     ```
   - 点击"创建"按钮
   - 确认群聊创建成功并自动进入

2. **验证群聊功能**
   - 确认群聊出现在左侧列表中
   - 确认右侧显示聊天窗口
   - 确认用户列表显示当前用户为群主

### 第三步：发送消息

1. **发送文本消息**
   - 在消息输入框输入: "大家好，欢迎加入项目讨论组！"
   - 按 Enter 键发送
   - 确认消息出现在聊天窗口中
   - 确认消息显示发送时间和发送者信息

2. **发送表情消息**
   - 点击输入框左侧的 😀 按钮
   - 选择几个表情符号
   - 发送包含表情的消息
   - 确认表情正确显示

3. **测试换行功能**
   - 输入多行文本，使用 Shift + Enter 换行
   - 确认换行功能正常工作

### 第四步：文件分享

1. **上传图片文件**
   - 点击输入框左侧的 📎 按钮
   - 选择一张图片文件 (JPG/PNG)
   - 等待上传完成
   - 确认图片在聊天中正确显示
   - 点击图片确认可以预览

2. **上传文档文件**
   - 再次点击 📎 按钮
   - 选择一个文档文件 (PDF/Word)
   - 等待上传完成
   - 确认文件信息正确显示
   - 点击下载链接测试下载功能

### 第五步：创建私聊

1. **开启新浏览器窗口**
   - 打开新的浏览器窗口或无痕模式
   - 登录另一个测试账号: <EMAIL> / password123

2. **创建私聊**
   - 在第一个用户的聊天界面
   - 点击用户列表中的其他用户
   - 或者手动创建私聊
   - 确认私聊房间创建成功

3. **测试私聊功能**
   - 在私聊中发送消息
   - 在另一个浏览器窗口确认收到消息
   - 测试双向消息发送

### 第六步：实时功能测试

1. **在线状态测试**
   - 确认用户在线状态正确显示
   - 关闭一个浏览器窗口
   - 确认另一个窗口显示用户离线

2. **正在输入测试**
   - 在一个窗口开始输入消息（不发送）
   - 在另一个窗口确认显示"正在输入..."
   - 停止输入，确认提示消失

3. **消息已读测试**
   - 发送消息后确认显示"未读"状态
   - 在接收方查看消息
   - 确认发送方显示"已读"状态

### 第七步：高级功能测试

1. **消息反应功能**
   - 点击任意消息
   - 选择表情反应 (👍 ❤️ 😂 等)
   - 确认反应正确显示
   - 测试取消反应功能

2. **消息搜索功能**
   - 发送几条包含关键词的消息
   - 使用搜索功能查找消息
   - 确认搜索结果正确

3. **文件权限测试**
   - 上传文件并设置权限
   - 用不同用户测试文件访问
   - 确认权限控制正常工作

### 第八步：移动端测试

1. **响应式设计测试**
   - 调整浏览器窗口大小模拟移动设备
   - 确认界面自适应调整
   - 测试移动端导航功能

2. **触摸操作测试**
   - 使用开发者工具模拟触摸设备
   - 测试滑动、点击等操作
   - 确认移动端体验良好

## 🔍 功能验证清单

### 基础功能 ✅
- [ ] 用户登录和认证
- [ ] 聊天室列表显示
- [ ] 创建群聊功能
- [ ] 发送文本消息
- [ ] 消息时间显示
- [ ] 用户头像和姓名显示

### 高级功能 ✅
- [ ] 文件上传和分享
- [ ] 图片预览功能
- [ ] 文件下载功能
- [ ] 私聊创建和使用
- [ ] 在线状态显示
- [ ] 正在输入提示

### 实时功能 ✅
- [ ] Socket.io连接正常
- [ ] 消息实时推送
- [ ] 用户上线/下线通知
- [ ] 消息已读状态同步
- [ ] 断线重连功能

### 用户体验 ✅
- [ ] 界面响应速度
- [ ] 移动端适配
- [ ] 错误处理和提示
- [ ] 加载状态显示
- [ ] 操作反馈及时

## 🐛 常见问题排查

### 1. Socket连接失败
```javascript
// 检查浏览器控制台是否有错误
// 确认后端Socket.io服务正常启动
// 检查端口和CORS配置
```

### 2. 消息发送失败
```javascript
// 检查网络连接
// 确认用户认证状态
// 查看后端日志错误信息
```

### 3. 文件上传失败
```javascript
// 检查文件大小是否超过限制 (10MB)
// 确认文件类型是否支持
// 检查uploads目录权限
```

### 4. 实时功能不工作
```javascript
// 确认Socket.io服务启动
// 检查防火墙设置
// 验证WebSocket连接
```

## 📊 性能测试

### 1. 并发用户测试
- 同时打开多个浏览器窗口
- 模拟多用户同时聊天
- 观察系统响应时间

### 2. 大文件上传测试
- 上传接近10MB的文件
- 测试上传进度显示
- 验证上传完成后的处理

### 3. 长时间运行测试
- 保持聊天窗口打开数小时
- 发送大量消息
- 观察内存使用情况

## 📝 演示报告模板

```markdown
# 聊天功能演示报告

## 演示环境
- 操作系统: Windows 10
- 浏览器: Chrome 120.0
- 网络环境: 本地开发环境

## 功能测试结果
| 功能项 | 测试结果 | 备注 |
|--------|----------|------|
| 用户登录 | ✅ 通过 | 响应速度正常 |
| 创建群聊 | ✅ 通过 | 界面友好 |
| 发送消息 | ✅ 通过 | 实时性良好 |
| 文件上传 | ✅ 通过 | 支持多种格式 |
| 私聊功能 | ✅ 通过 | 创建便捷 |
| 移动端适配 | ✅ 通过 | 响应式设计良好 |

## 发现的问题
1. 问题描述
2. 重现步骤
3. 预期结果
4. 实际结果

## 改进建议
1. 功能改进建议
2. 性能优化建议
3. 用户体验改进建议
```

通过这个详细的演示步骤，您可以全面测试和验证OA系统聊天功能的完整性和可用性。
