/* 聊天室列表容器 */
.chat-room-list {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
}

.chat-room-list.loading {
  justify-content: center;
  align-items: center;
}

.loading-spinner {
  padding: 40px;
  text-align: center;
  color: #666;
  font-size: 14px;
}

/* 头部 */
.room-list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  background: #fff;
  position: sticky;
  top: 0;
  z-index: 10;
}

.room-list-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.create-room-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #007bff;
  color: white;
  border: none;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.create-room-btn:hover {
  background: #0056b3;
}

/* 控制区域 */
.room-list-controls {
  padding: 12px 20px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.search-box {
  margin-bottom: 12px;
}

.search-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 20px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s;
}

.search-input:focus {
  border-color: #007bff;
}

.filter-tabs {
  display: flex;
  gap: 4px;
}

.filter-tab {
  flex: 1;
  padding: 6px 12px;
  border: none;
  background: transparent;
  color: #666;
  font-size: 12px;
  cursor: pointer;
  border-radius: 12px;
  transition: all 0.2s;
}

.filter-tab.active {
  background: #007bff;
  color: white;
}

.filter-tab:hover:not(.active) {
  background: #f0f8ff;
  color: #007bff;
}

/* 房间列表 */
.room-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
}

.room-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-left: 3px solid transparent;
}

.room-item:hover {
  background-color: #f8f9fa;
}

.room-item.selected {
  background-color: #e3f2fd;
  border-left-color: #007bff;
}

.room-avatar {
  position: relative;
  margin-right: 12px;
  flex-shrink: 0;
}

.room-avatar .avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #f0f0f0;
}

.online-indicator {
  position: absolute;
  top: -2px;
  right: -2px;
  background: #28a745;
  color: white;
  font-size: 10px;
  padding: 2px 4px;
  border-radius: 8px;
  min-width: 16px;
  text-align: center;
  line-height: 1;
}

.room-info {
  flex: 1;
  min-width: 0;
}

.room-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
}

.room-name {
  font-weight: 500;
  color: #333;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
}

.room-time {
  font-size: 11px;
  color: #999;
  flex-shrink: 0;
}

.room-preview {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.last-message {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 180px;
}

.no-message {
  font-size: 12px;
  color: #999;
  font-style: italic;
}

.unread-badge {
  background: #dc3545;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
  line-height: 1.2;
  flex-shrink: 0;
}

/* 空状态 */
.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.empty-state p {
  color: #666;
  margin-bottom: 16px;
  font-size: 14px;
}

.create-first-room-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.create-first-room-btn:hover {
  background: #0056b3;
}

/* 创建房间模态框 */
.create-room-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 400px;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  color: #999;
  cursor: pointer;
  padding: 4px;
  line-height: 1;
}

.close-btn:hover {
  color: #666;
}

.create-room-form {
  padding: 20px;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s;
  box-sizing: border-box;
}

.form-group input:focus,
.form-group textarea:focus {
  border-color: #007bff;
}

.form-group textarea {
  resize: vertical;
  min-height: 60px;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 20px;
}

.cancel-btn,
.submit-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.cancel-btn {
  background: #f8f9fa;
  color: #666;
}

.cancel-btn:hover {
  background: #e9ecef;
}

.submit-btn {
  background: #007bff;
  color: white;
}

.submit-btn:hover {
  background: #0056b3;
}

/* 错误消息 */
.error-message {
  padding: 12px 20px;
  background: #f8d7da;
  color: #721c24;
  border-top: 1px solid #f5c6cb;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.error-message button {
  background: none;
  border: none;
  color: #721c24;
  text-decoration: underline;
  cursor: pointer;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .room-list-header {
    padding: 12px 16px;
  }

  .room-list-controls {
    padding: 8px 16px;
  }

  .room-item {
    padding: 10px 16px;
  }

  .room-avatar .avatar {
    width: 40px;
    height: 40px;
  }

  .room-name {
    max-width: 120px;
  }

  .last-message {
    max-width: 140px;
  }

  .modal-content {
    margin: 20px;
    width: calc(100% - 40px);
  }

  .create-room-form {
    padding: 16px;
  }
}
