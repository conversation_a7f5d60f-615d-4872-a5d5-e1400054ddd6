/* 导航栏样式 */
.navigation {
  background: #fff;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.nav-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  height: 60px;
}

/* Logo */
.nav-logo {
  flex-shrink: 0;
}

.logo-link {
  display: flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  color: #333;
  font-weight: 600;
  font-size: 18px;
}

.logo-icon {
  font-size: 24px;
}

.logo-text {
  color: #007bff;
}

/* 主导航菜单 */
.nav-menu {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  justify-content: center;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  text-decoration: none;
  color: #666;
  border-radius: 8px;
  transition: all 0.2s ease;
  position: relative;
  font-size: 14px;
  font-weight: 500;
}

.nav-item:hover {
  background-color: #f8f9fa;
  color: #007bff;
}

.nav-item.active {
  background-color: #e3f2fd;
  color: #007bff;
}

.nav-icon {
  font-size: 16px;
}

.nav-text {
  white-space: nowrap;
}

/* 未读消息徽章 */
.unread-badge {
  position: absolute;
  top: 2px;
  right: 8px;
  background: #dc3545;
  color: white;
  font-size: 10px;
  padding: 2px 5px;
  border-radius: 10px;
  min-width: 16px;
  text-align: center;
  line-height: 1.2;
  font-weight: 600;
}

/* 用户菜单 */
.nav-user {
  position: relative;
  flex-shrink: 0;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  cursor: pointer;
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.user-profile:hover {
  background-color: #f8f9fa;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e0e0e0;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  max-width: 120px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dropdown-arrow {
  font-size: 10px;
  color: #999;
  transition: transform 0.2s ease;
}

.user-profile:hover .dropdown-arrow {
  transform: rotate(180deg);
}

/* 用户下拉菜单 */
.user-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 200px;
  z-index: 1001;
  margin-top: 4px;
}

.user-info {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.user-name-full {
  font-weight: 600;
  color: #333;
  margin-bottom: 2px;
}

.user-email {
  font-size: 12px;
  color: #666;
}

.menu-divider {
  height: 1px;
  background: #f0f0f0;
  margin: 4px 0;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  text-decoration: none;
  color: #333;
  font-size: 14px;
  transition: background-color 0.2s ease;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
}

.menu-item:hover {
  background-color: #f8f9fa;
}

.menu-icon {
  font-size: 14px;
  width: 16px;
  text-align: center;
}

.logout-btn {
  color: #dc3545;
}

.logout-btn:hover {
  background-color: #fff5f5;
}

/* 移动端菜单 */
.mobile-menu-toggle {
  display: none;
}

.menu-toggle-btn {
  background: none;
  border: none;
  font-size: 20px;
  color: #666;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
}

.menu-toggle-btn:hover {
  background-color: #f8f9fa;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-container {
    padding: 0 16px;
    height: 56px;
  }

  .nav-menu {
    display: none;
  }

  .mobile-menu-toggle {
    display: block;
  }

  .logo-text {
    display: none;
  }

  .user-name {
    display: none;
  }

  .user-menu {
    right: 0;
    left: auto;
    min-width: 180px;
  }

  .nav-item {
    padding: 12px 16px;
    font-size: 16px;
  }

  .nav-icon {
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  .nav-container {
    padding: 0 12px;
  }

  .logo-link {
    font-size: 16px;
  }

  .logo-icon {
    font-size: 20px;
  }

  .user-avatar {
    width: 28px;
    height: 28px;
  }

  .user-menu {
    min-width: 160px;
  }
}

/* 点击外部关闭菜单 */
.nav-user.menu-open .user-menu {
  display: block;
}

/* 动画效果 */
.user-menu {
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 焦点样式 */
.nav-item:focus,
.user-profile:focus,
.menu-item:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .navigation {
    border-bottom: 2px solid #000;
  }

  .nav-item {
    border: 1px solid transparent;
  }

  .nav-item:hover,
  .nav-item.active {
    border-color: #007bff;
  }

  .user-menu {
    border: 2px solid #000;
  }
}
