# OA模块集成使用示例

## 📋 概述

本文档提供了将OA系统作为子模块集成到其他网页系统的完整示例，包括不同场景下的集成方案和代码示例。

## 🚀 快速集成示例

### 1. Express.js 应用集成

```javascript
// app.js - 主应用入口文件
const express = require('express');
const OAModuleAdapter = require('./integration/OAModuleAdapter');

const app = express();

// 基础中间件
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 初始化OA模块
const oaModule = new OAModuleAdapter({
    module: {
        prefix: '/api/oa'
    },
    auth: {
        provider: 'external',
        validateUrl: 'http://main-system/api/auth/validate',
        userInfoUrl: 'http://main-system/api/auth/me'
    },
    database: {
        type: 'mongodb',
        host: 'localhost',
        port: 27017,
        name: 'main_system_oa'
    },
    userSync: {
        enabled: true,
        syncUrl: 'http://main-system/api/users',
        syncInterval: 3600000 // 1小时同步一次
    }
});

// 启动应用
async function startApp() {
    try {
        // 初始化OA模块
        await oaModule.initialize();
        
        // 挂载OA路由
        app.use('/api/oa', oaModule.getRoutes());
        
        // 主系统路由
        app.use('/api/main', require('./routes/main'));
        
        // 健康检查
        app.get('/health', (req, res) => {
            res.json({
                status: 'OK',
                oa: oaModule.getStatus()
            });
        });
        
        const PORT = process.env.PORT || 3000;
        app.listen(PORT, () => {
            console.log(`服务器运行在端口 ${PORT}`);
        });
        
    } catch (error) {
        console.error('应用启动失败:', error);
        process.exit(1);
    }
}

startApp();
```

### 2. 自定义认证适配器

```javascript
// adapters/CustomAuthAdapter.js
class CustomAuthAdapter {
    constructor(config) {
        this.config = config;
        this.apiClient = new ApiClient(config.baseUrl);
    }
    
    async validateToken(token) {
        try {
            const response = await this.apiClient.post('/auth/validate', {
                token: token
            }, {
                headers: {
                    'X-API-Key': this.config.apiKey
                }
            });
            
            return {
                valid: response.data.success,
                userId: response.data.user?.id,
                permissions: response.data.user?.permissions || [],
                expiresAt: response.data.expiresAt ? new Date(response.data.expiresAt) : null
            };
            
        } catch (error) {
            console.error('Token验证失败:', error);
            return { valid: false };
        }
    }
    
    async getUserInfo(token) {
        const response = await this.apiClient.get('/user/profile', {
            headers: {
                'Authorization': `Bearer ${token}`,
                'X-API-Key': this.config.apiKey
            }
        });
        
        // 转换用户数据格式
        return this.transformUserData(response.data);
    }
    
    transformUserData(userData) {
        return {
            id: userData.user_id,
            username: userData.login_name,
            email: userData.email_address,
            name: userData.display_name,
            avatar: userData.profile_image,
            roles: userData.user_roles || [],
            permissions: userData.user_permissions || [],
            department: userData.department_info ? {
                id: userData.department_info.dept_id,
                name: userData.department_info.dept_name
            } : null
        };
    }
}

module.exports = CustomAuthAdapter;
```

### 3. 用户数据同步适配器

```javascript
// adapters/UserSyncAdapter.js
class UserSyncAdapter {
    constructor(config) {
        this.config = config;
        this.apiClient = new ApiClient(config.syncUrl);
        this.userService = new UserService();
    }
    
    async syncAllUsers() {
        try {
            let page = 1;
            let hasMore = true;
            const results = { success: 0, failed: 0, errors: [] };
            
            while (hasMore) {
                const response = await this.apiClient.get('/users', {
                    params: {
                        page: page,
                        limit: this.config.batchSize || 100,
                        include_inactive: false
                    }
                });
                
                const users = response.data.users || [];
                
                for (const user of users) {
                    try {
                        await this.syncUser(user);
                        results.success++;
                    } catch (error) {
                        results.failed++;
                        results.errors.push(`用户 ${user.username}: ${error.message}`);
                    }
                }
                
                hasMore = users.length === this.config.batchSize;
                page++;
            }
            
            return results;
            
        } catch (error) {
            throw new Error(`用户同步失败: ${error.message}`);
        }
    }
    
    async syncUser(externalUser) {
        const internalUser = this.transformUser(externalUser);
        
        // 检查用户是否已存在
        const existingUser = await this.userService.findByExternalId(externalUser.id);
        
        if (existingUser) {
            // 更新现有用户
            return await this.userService.update(existingUser.id, internalUser);
        } else {
            // 创建新用户
            return await this.userService.create(internalUser);
        }
    }
    
    transformUser(externalUser) {
        return {
            externalId: externalUser.id,
            username: externalUser.username,
            email: externalUser.email,
            name: externalUser.display_name || externalUser.name,
            avatar: externalUser.avatar_url,
            phone: externalUser.phone_number,
            status: this.mapUserStatus(externalUser.status),
            roles: externalUser.roles || ['employee'],
            department: externalUser.department ? {
                externalId: externalUser.department.id,
                name: externalUser.department.name
            } : null
        };
    }
    
    mapUserStatus(externalStatus) {
        const statusMap = {
            'active': 'active',
            'inactive': 'inactive',
            'disabled': 'suspended',
            'deleted': 'deleted'
        };
        
        return statusMap[externalStatus] || 'inactive';
    }
}

module.exports = UserSyncAdapter;
```

## 🔧 配置示例

### 1. 开发环境配置

```json
// config/development.json
{
  "oa": {
    "module": {
      "name": "OA-Development",
      "prefix": "/api/oa",
      "debug": true
    },
    "auth": {
      "provider": "external",
      "validateUrl": "http://localhost:8080/api/auth/validate",
      "userInfoUrl": "http://localhost:8080/api/auth/me",
      "timeout": 5000
    },
    "database": {
      "type": "mongodb",
      "host": "localhost",
      "port": 27017,
      "name": "dev_oa_system"
    },
    "file": {
      "uploadPath": "./uploads/dev",
      "maxSize": 5242880,
      "enablePreview": true
    },
    "socket": {
      "enabled": true,
      "namespace": "/oa",
      "cors": {
        "origin": "http://localhost:3000",
        "credentials": true
      }
    },
    "userSync": {
      "enabled": true,
      "syncUrl": "http://localhost:8080/api/users",
      "syncInterval": 300000,
      "batchSize": 50
    }
  }
}
```

### 2. 生产环境配置

```json
// config/production.json
{
  "oa": {
    "module": {
      "name": "OA-Production",
      "prefix": "/api/oa",
      "debug": false
    },
    "auth": {
      "provider": "external",
      "validateUrl": "https://api.company.com/auth/validate",
      "userInfoUrl": "https://api.company.com/auth/me",
      "timeout": 10000,
      "retries": 3
    },
    "database": {
      "type": "mongodb",
      "host": "mongodb.company.com",
      "port": 27017,
      "name": "prod_oa_system",
      "options": {
        "ssl": true,
        "authSource": "admin"
      }
    },
    "file": {
      "storage": "oss",
      "uploadPath": "/data/uploads",
      "maxSize": 10485760,
      "enablePreview": true,
      "enableVersionControl": true
    },
    "socket": {
      "enabled": true,
      "namespace": "/oa",
      "cors": {
        "origin": ["https://app.company.com", "https://admin.company.com"],
        "credentials": true
      }
    },
    "userSync": {
      "enabled": true,
      "syncUrl": "https://api.company.com/users",
      "syncInterval": 3600000,
      "batchSize": 100
    }
  }
}
```

## 🎯 集成场景示例

### 1. 微服务架构集成

```javascript
// microservice-integration.js
const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const OAModuleAdapter = require('./integration/OAModuleAdapter');

const app = express();

// OA模块配置
const oaModule = new OAModuleAdapter({
    auth: {
        provider: 'external',
        validateUrl: 'http://auth-service:3001/validate',
        userInfoUrl: 'http://user-service:3002/me'
    },
    database: {
        host: 'mongodb-service',
        name: 'oa_microservice'
    }
});

// 初始化并启动
async function start() {
    await oaModule.initialize();
    
    // OA模块路由
    app.use('/api/oa', oaModule.getRoutes());
    
    // 代理其他微服务
    app.use('/api/auth', createProxyMiddleware({
        target: 'http://auth-service:3001',
        changeOrigin: true
    }));
    
    app.use('/api/users', createProxyMiddleware({
        target: 'http://user-service:3002',
        changeOrigin: true
    }));
    
    app.listen(3000);
}

start().catch(console.error);
```

### 2. 单页应用(SPA)集成

```javascript
// spa-integration.js
const express = require('express');
const path = require('path');
const OAModuleAdapter = require('./integration/OAModuleAdapter');

const app = express();

// 静态文件服务
app.use(express.static(path.join(__dirname, 'public')));

// OA模块
const oaModule = new OAModuleAdapter({
    auth: {
        provider: 'internal', // 使用内部认证
        jwtSecret: process.env.JWT_SECRET
    }
});

async function start() {
    await oaModule.initialize();
    
    // API路由
    app.use('/api/oa', oaModule.getRoutes());
    
    // SPA路由 - 所有其他请求返回index.html
    app.get('*', (req, res) => {
        res.sendFile(path.join(__dirname, 'public/index.html'));
    });
    
    app.listen(3000);
}

start().catch(console.error);
```

### 3. 多租户集成

```javascript
// multi-tenant-integration.js
const express = require('express');
const OAModuleAdapter = require('./integration/OAModuleAdapter');

const app = express();

// 租户识别中间件
app.use((req, res, next) => {
    const tenantId = req.headers['x-tenant-id'] || req.query.tenant;
    if (!tenantId) {
        return res.status(400).json({ error: '缺少租户标识' });
    }
    req.tenantId = tenantId;
    next();
});

// 为每个租户创建OA模块实例
const oaModules = new Map();

async function getOAModule(tenantId) {
    if (!oaModules.has(tenantId)) {
        const module = new OAModuleAdapter({
            database: {
                name: `oa_tenant_${tenantId}`
            },
            auth: {
                validateUrl: `http://auth-service/validate/${tenantId}`
            }
        });
        
        await module.initialize();
        oaModules.set(tenantId, module);
    }
    
    return oaModules.get(tenantId);
}

// 动态路由处理
app.use('/api/oa', async (req, res, next) => {
    try {
        const oaModule = await getOAModule(req.tenantId);
        const router = oaModule.getRoutes();
        router(req, res, next);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

app.listen(3000);
```

## 🔌 前端集成示例

### 1. React集成

```jsx
// OAProvider.jsx
import React, { createContext, useContext, useEffect, useState } from 'react';
import axios from 'axios';
import io from 'socket.io-client';

const OAContext = createContext();

export const useOA = () => {
    const context = useContext(OAContext);
    if (!context) {
        throw new Error('useOA must be used within OAProvider');
    }
    return context;
};

export const OAProvider = ({ children, config }) => {
    const [socket, setSocket] = useState(null);
    const [user, setUser] = useState(null);
    const [loading, setLoading] = useState(true);

    // 配置axios
    const apiClient = axios.create({
        baseURL: config.apiBaseUrl || '/api/oa',
        headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
    });

    // 初始化Socket连接
    useEffect(() => {
        const token = localStorage.getItem('token');
        if (token) {
            const socketInstance = io(config.socketUrl || '', {
                auth: { token },
                namespace: config.socketNamespace || '/oa'
            });

            socketInstance.on('connect', () => {
                console.log('OA Socket连接成功');
            });

            setSocket(socketInstance);

            return () => socketInstance.disconnect();
        }
    }, [config]);

    // 获取用户信息
    useEffect(() => {
        const fetchUser = async () => {
            try {
                const response = await apiClient.get('/auth/me');
                setUser(response.data.user);
            } catch (error) {
                console.error('获取用户信息失败:', error);
            } finally {
                setLoading(false);
            }
        };

        fetchUser();
    }, []);

    const value = {
        apiClient,
        socket,
        user,
        loading,
        // OA功能方法
        sendMessage: (roomId, content) => {
            if (socket) {
                socket.emit('send_message', { roomId, content });
            }
        },
        uploadFile: async (file, projectId) => {
            const formData = new FormData();
            formData.append('file', file);
            if (projectId) formData.append('projectId', projectId);
            
            return await apiClient.post('/files/upload', formData);
        }
    };

    return (
        <OAContext.Provider value={value}>
            {children}
        </OAContext.Provider>
    );
};
```

### 2. Vue.js集成

```javascript
// oa-plugin.js
import axios from 'axios';
import io from 'socket.io-client';

export default {
    install(app, options) {
        const config = {
            apiBaseUrl: '/api/oa',
            socketNamespace: '/oa',
            ...options
        };

        // 创建API客户端
        const apiClient = axios.create({
            baseURL: config.apiBaseUrl
        });

        // 请求拦截器 - 添加token
        apiClient.interceptors.request.use(config => {
            const token = localStorage.getItem('token');
            if (token) {
                config.headers.Authorization = `Bearer ${token}`;
            }
            return config;
        });

        // Socket连接
        let socket = null;
        const connectSocket = (token) => {
            if (socket) socket.disconnect();
            
            socket = io(config.socketUrl || '', {
                auth: { token },
                namespace: config.socketNamespace
            });
            
            return socket;
        };

        // 全局属性
        app.config.globalProperties.$oa = {
            api: apiClient,
            socket: null,
            connect: connectSocket,
            
            // 便捷方法
            async getProjects() {
                const response = await apiClient.get('/projects');
                return response.data;
            },
            
            async uploadFile(file, projectId) {
                const formData = new FormData();
                formData.append('file', file);
                if (projectId) formData.append('projectId', projectId);
                
                const response = await apiClient.post('/files/upload', formData);
                return response.data;
            },
            
            sendMessage(roomId, content) {
                if (this.socket) {
                    this.socket.emit('send_message', { roomId, content });
                }
            }
        };

        // 提供依赖注入
        app.provide('oa', app.config.globalProperties.$oa);
    }
};
```

## 📝 部署脚本示例

### 1. Docker部署

```dockerfile
# Dockerfile
FROM node:16-alpine

WORKDIR /app

# 复制package文件
COPY package*.json ./
RUN npm ci --only=production

# 复制应用代码
COPY . .

# 创建上传目录
RUN mkdir -p uploads

# 暴露端口
EXPOSE 3000

# 启动应用
CMD ["node", "app.js"]
```

```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - MONGODB_URI=mongodb://mongo:27017/oa_system
    depends_on:
      - mongo
    volumes:
      - ./uploads:/app/uploads

  mongo:
    image: mongo:5.0
    ports:
      - "27017:27017"
    volumes:
      - mongo_data:/data/db

volumes:
  mongo_data:
```

### 2. PM2部署

```javascript
// ecosystem.config.js
module.exports = {
    apps: [{
        name: 'oa-system',
        script: 'app.js',
        instances: 'max',
        exec_mode: 'cluster',
        env: {
            NODE_ENV: 'development',
            PORT: 3000
        },
        env_production: {
            NODE_ENV: 'production',
            PORT: 3000,
            MONGODB_URI: 'mongodb://localhost:27017/oa_production'
        }
    }]
};
```

这些示例展示了如何在不同场景下集成OA模块，包括配置管理、认证适配、数据同步和前端集成等各个方面。通过这些示例，可以快速将OA系统集成到现有的网页系统中。
